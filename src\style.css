/* 数位时空穿梭机样式 */
:root {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 主题色彩 */
  --primary-color: #00d4ff;
  --secondary-color: #ff6b35;
  --accent-color: #ffd700;
  --bg-dark: #0a0a0a;
  --bg-panel: rgba(0, 0, 0, 0.8);
  --text-light: #ffffff;
  --text-muted: #cccccc;
  --border-color: rgba(255, 255, 255, 0.2);

  color: var(--text-light);
  background-color: var(--bg-dark);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 3D画布 */
#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* UI容器 */
#ui-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

/* 时间轴控制器 */
#timeline-controller {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-panel);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 20px;
  min-width: 400px;
  pointer-events: auto;
}

.timeline-header h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.1em;
}

.timeline-slider-container {
  position: relative;
}

#timeline-slider {
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

#timeline-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--accent-color);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

#timeline-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--accent-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.timeline-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  font-size: 0.9em;
  color: var(--text-muted);
}

.timeline-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.current-era {
  color: var(--primary-color);
  font-weight: bold;
  font-size: 1em;
  margin-bottom: 2px;
}

.current-year {
  color: var(--accent-color);
  font-size: 0.8em;
}

.next-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.next-era {
  color: var(--text-muted);
  font-size: 0.8em;
  opacity: 0.7;
}

/* 信息面板 */
#info-panel {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 350px;
  max-height: 70vh;
  background: var(--bg-panel);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  overflow: hidden;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  pointer-events: auto;
}

#info-panel.active {
  transform: translateX(0);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.info-header h3 {
  font-size: 1.2em;
  margin: 0;
}

#close-info {
  background: none;
  border: none;
  color: white;
  font-size: 1.5em;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

#close-info:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.info-content {
  padding: 20px;
  max-height: calc(70vh - 80px);
  overflow-y: auto;
}

#info-title {
  color: var(--primary-color);
  margin-bottom: 10px;
  font-size: 1.1em;
}

#info-description {
  color: var(--text-muted);
  line-height: 1.6;
  margin-bottom: 15px;
}

#info-specs {
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 5px 0;
}

.spec-label {
  color: var(--text-muted);
  font-weight: 500;
}

.spec-value {
  color: var(--text-light);
  text-align: right;
  max-width: 60%;
}

/* 导航控制 */
#navigation-controls {
  position: absolute;
  top: 30px;
  left: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: auto;
}

button {
  background: var(--bg-panel);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 20px;
  color: var(--text-light);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 0.9em;
}

button:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

button:active {
  transform: translateY(0);
}

/* 加载界面 */
#loading-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: opacity 0.5s ease;
}

#loading-screen.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-content {
  text-align: center;
  color: var(--text-light);
}

.loading-content h2 {
  font-size: 2.5em;
  margin-bottom: 30px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  color: var(--text-muted);
  font-size: 1.1em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #timeline-controller {
    bottom: 20px;
    left: 20px;
    right: 20px;
    transform: none;
    min-width: auto;
  }

  #info-panel {
    top: 20px;
    right: 20px;
    left: 20px;
    width: auto;
  }

  #navigation-controls {
    top: 20px;
    left: 20px;
  }

  .loading-content h2 {
    font-size: 2em;
  }
}
