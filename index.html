<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数位时空穿梭机 - Digital Chrono-Cruiser</title>
  </head>
  <body>
    <div id="app">
      <!-- 3D场景容器 -->
      <canvas id="three-canvas"></canvas>

      <!-- UI控制面板 -->
      <div id="ui-container">
        <!-- 时间轴控制器 -->
        <div id="timeline-controller">
          <div class="timeline-header">
            <h3>时间轴控制</h3>
          </div>
          <div class="timeline-slider-container">
            <input type="range" id="timeline-slider" min="0" max="100" value="0" />
            <div class="timeline-labels">
              <span>古代</span>
              <span>现代</span>
            </div>
          </div>
        </div>

        <!-- 信息面板 -->
        <div id="info-panel">
          <div class="info-header">
            <h3>历史信息</h3>
            <button id="close-info">×</button>
          </div>
          <div class="info-content">
            <h4 id="info-title">选择一个历史节点</h4>
            <p id="info-description">点击时间长廊中的任意模型来了解计算机发展历史</p>
            <div id="info-specs"></div>
          </div>
        </div>

        <!-- 导航控制 -->
        <div id="navigation-controls">
          <button id="reset-camera">重置视角</button>
          <button id="toggle-mode">切换模式</button>
        </div>
      </div>

      <!-- 加载界面 -->
      <div id="loading-screen">
        <div class="loading-content">
          <h2>数位时空穿梭机</h2>
          <div class="loading-spinner"></div>
          <p>正在加载历史长廊...</p>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
