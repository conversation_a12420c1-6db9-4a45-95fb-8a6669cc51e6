import * as THREE from 'three'
import { OrbitControls } from 'three-stdlib'
import { SceneManager } from './SceneManager'
import { TimelineController } from './TimelineController'
import { UIManager } from './UIManager'
import { HistoryGallery } from './HistoryGallery'
import { ComponentEvolution } from './ComponentEvolution'

/**
 * 数位时空穿梭机主类
 * 负责整个应用的初始化和协调各个模块
 */
export class DigitalChronoCruiser {
  private canvas!: HTMLCanvasElement
  private renderer!: THREE.WebGLRenderer
  private scene!: THREE.Scene
  private camera!: THREE.PerspectiveCamera
  private controls!: OrbitControls
  
  // 核心模块
  private sceneManager!: SceneManager
  private timelineController!: TimelineController
  private uiManager!: UIManager
  private historyGallery!: HistoryGallery
  private componentEvolution!: ComponentEvolution
  
  // 应用状态
  private isInitialized = false
  private currentMode: 'gallery' | 'evolution' = 'gallery'
  
  constructor() {
    this.setupCanvas()
    this.setupRenderer()
    this.setupScene()
    this.setupCamera()
    this.setupControls()
    this.setupModules()
    this.setupEventListeners()
  }

  /**
   * 初始化应用
   */
  async init(): Promise<void> {
    try {
      // 显示加载界面
      this.showLoadingScreen()
      
      // 初始化各个模块
      await this.initializeModules()
      
      // 开始渲染循环
      this.startRenderLoop()
      
      // 隐藏加载界面
      this.hideLoadingScreen()
      
      this.isInitialized = true
      console.log('数位时空穿梭机初始化完成')
    } catch (error) {
      console.error('初始化失败:', error)
    }
  }

  /**
   * 设置画布
   */
  private setupCanvas(): void {
    this.canvas = document.getElementById('three-canvas') as HTMLCanvasElement
    if (!this.canvas) {
      throw new Error('找不到3D画布元素')
    }
  }

  /**
   * 设置渲染器
   */
  private setupRenderer(): void {
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: true
    })
    
    this.renderer.setSize(window.innerWidth, window.innerHeight)
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    this.renderer.outputColorSpace = THREE.SRGBColorSpace
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1.2
  }

  /**
   * 设置场景
   */
  private setupScene(): void {
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0x0a0a0a)
    this.scene.fog = new THREE.Fog(0x0a0a0a, 50, 200)
  }

  /**
   * 设置相机
   */
  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    this.camera.position.set(0, 10, 30)
  }

  /**
   * 设置控制器
   */
  private setupControls(): void {
    this.controls = new OrbitControls(this.camera, this.canvas)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.maxDistance = 100
    this.controls.minDistance = 5
    this.controls.maxPolarAngle = Math.PI * 0.8
  }

  /**
   * 设置模块
   */
  private setupModules(): void {
    this.sceneManager = new SceneManager(this.scene)
    this.timelineController = new TimelineController()
    this.uiManager = new UIManager()
    this.historyGallery = new HistoryGallery(this.scene)
    this.componentEvolution = new ComponentEvolution(this.scene)
  }

  /**
   * 初始化模块
   */
  private async initializeModules(): Promise<void> {
    await this.sceneManager.init()
    await this.historyGallery.init()
    await this.componentEvolution.init()

    // 设置相机引用
    this.historyGallery.setCamera(this.camera)
    this.componentEvolution.setCamera(this.camera)

    this.timelineController.init()
    this.uiManager.init()

    // 设置模块间的通信
    this.setupModuleCommunication()
  }

  /**
   * 设置模块间通信
   */
  private setupModuleCommunication(): void {
    // 时间轴变化事件
    this.timelineController.onTimeChangeCallback((time: number) => {
      if (this.currentMode === 'gallery') {
        this.historyGallery.updateTimeline(time)
      } else {
        this.componentEvolution.updateTimeline(time)
      }
    })

    // 历史节点点击事件
    this.historyGallery.onNodeClick((nodeData: any) => {
      this.uiManager.showInfoPanel(nodeData)
    })

    // 组件演进点击事件
    this.componentEvolution.onComponentClick((componentData: any) => {
      this.uiManager.showInfoPanel(componentData)
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this))
    
    // UI按钮事件
    this.setupUIEvents()
  }

  /**
   * 设置UI事件
   */
  private setupUIEvents(): void {
    // 重置相机按钮
    const resetCameraBtn = document.getElementById('reset-camera')
    resetCameraBtn?.addEventListener('click', () => {
      this.resetCamera()
    })
    
    // 切换模式按钮
    const toggleModeBtn = document.getElementById('toggle-mode')
    toggleModeBtn?.addEventListener('click', () => {
      this.toggleMode()
    })
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(window.innerWidth, window.innerHeight)
  }

  /**
   * 重置相机位置
   */
  private resetCamera(): void {
    this.camera.position.set(0, 10, 30)
    this.controls.target.set(0, 0, 0)
    this.controls.update()
  }

  /**
   * 切换模式
   */
  private toggleMode(): void {
    this.currentMode = this.currentMode === 'gallery' ? 'evolution' : 'gallery'
    
    if (this.currentMode === 'gallery') {
      this.historyGallery.show()
      this.componentEvolution.hide()
    } else {
      this.historyGallery.hide()
      this.componentEvolution.show()
    }
    
    // 更新UI
    const toggleBtn = document.getElementById('toggle-mode')
    if (toggleBtn) {
      toggleBtn.textContent = this.currentMode === 'gallery' ? '组件演进' : '历史长廊'
    }
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      requestAnimationFrame(animate)
      
      // 更新控制器
      this.controls.update()
      
      // 更新各个模块
      if (this.isInitialized) {
        this.sceneManager.update()
        this.historyGallery.update()
        this.componentEvolution.update()
      }
      
      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    }
    
    animate()
  }

  /**
   * 显示加载界面
   */
  private showLoadingScreen(): void {
    const loadingScreen = document.getElementById('loading-screen')
    if (loadingScreen) {
      loadingScreen.classList.remove('hidden')
    }
  }

  /**
   * 隐藏加载界面
   */
  private hideLoadingScreen(): void {
    const loadingScreen = document.getElementById('loading-screen')
    if (loadingScreen) {
      setTimeout(() => {
        loadingScreen.classList.add('hidden')
      }, 1000)
    }
  }

  /**
   * 销毁应用
   */
  dispose(): void {
    this.controls.dispose()
    this.renderer.dispose()
    this.sceneManager.dispose()
    this.historyGallery.dispose()
    this.componentEvolution.dispose()
  }
}
